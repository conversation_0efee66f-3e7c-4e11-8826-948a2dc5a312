import "css/App.css";
import Favorites from "./pages/Favorites.jsx";
import Home from "./pages/Home.jsx";
import { Routes, Route } from "react-router-dom";
import Navbars from "./components/Navbars.jsx";

function App() {
  return (
    <div>
      <Navbars/>  
      <main className="main-content">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/Home" element={<Home />} />
          <Route path="/favorites" element={<Favorites />} />
        </Routes>
      </main>
    </div>
  );
}
export default App;
