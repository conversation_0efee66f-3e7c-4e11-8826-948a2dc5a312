import MovieCard from '../components/MovieCard.jsx';
function Home(){
    const [serchQuery, setSearchQuery]=useState("");

const movies = [
    {id: 1, title: "the maze runser", release_date: "2021", url: "https://link-to-image1.jpg"},
    {id: 2, title: "jane wake", release_date: "2024", url: "https://link-to-image2.jpg"},
    {id: 3, title: "Nowhere", release_date: "2010", url: "https://link-to-image3.jpg"},
    {id: 4, title: "Casa nigra", release_date: "2009", url: "https://link-to-image4.jpg"},
];   

    const hhandleSearch = () => {};


    return( <div className="home">
        <from onSubmit={hhandleSearch} className="search-form">
            <input 
            type="text" placeholder="Search movies..." 
            className='search-input'
            value={serchQuery}
            onChange={(e)=>setSearchQuery(e.target.value)}
            
            
            />
            <button 
            type="submit" 
            className='search-btn'>Search

            </button>

        </from>

        <div className="movie-grid">
            {movies.map((movie=><MovieCard movie={movie} key={movie.id} />))}

        </div>
    </div>

    
    );
}
export default Home;

//40:30
