import { useState, useEffect } from 'react';
import MovieCard from '../components/MovieCard.jsx';
import '../css/Home.css';
import { useMovieAPI } from '../hooks/useMovieAPI.js';

function Home(){
    const [searchQuery, setSearchQuery] = useState("");
    const { movies, loading, error, searchMovies, getPopularShows } = useMovieAPI();

    // جلب المسلسلات الشائعة عند تحميل الصفحة
    useEffect(() => {
        getPopularShows();
    }, [getPopularShows]);

    const handleSearch = (e) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            searchMovies(searchQuery);
        } else {
            getPopularShows(); // إذا كان البحث فارغ، اعرض المسلسلات الشائعة
        }
    };

    return( <div className="home">
        <form onSubmit={handleSearch} className="search-form">
            <input
                type="text"
                placeholder="Search movies..."
                className='search-input'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
            />
            <button
                type="submit"
                className='search-button'>
                Search
            </button>
        </form>

        {/* عرض رسالة التحميل */}
        {loading && (
            <div className="loading-message">
                <p>جاري تحميل المسلسلات...</p>
            </div>
        )}

        {/* عرض رسالة الخطأ */}
        {error && (
            <div className="error-message">
                <p>خطأ: {error}</p>
                <button onClick={getPopularShows} className="retry-btn">
                    إعادة المحاولة
                </button>
            </div>
        )}

        {/* عرض المسلسلات */}
        {!loading && !error && (
            <div className="movies-grid">
                {movies.length > 0 ? (
                    movies.map((movie) => (
                        <MovieCard movie={movie} key={movie.id} />
                    ))
                ) : (
                    <div className="no-results">
                        <p>لا توجد نتائج للبحث "{searchQuery}"</p>
                        <button onClick={getPopularShows} className="retry-btn">
                            عرض المسلسلات الشائعة
                        </button>
                    </div>
                )}
            </div>
        )}
    </div>

    
    );
}

export default Home;
