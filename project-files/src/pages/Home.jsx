import { useState } from 'react';
import MovieCard from '../components/MovieCard.jsx';
import '../css/Home.css';

function Home(){
    const [searchQuery, setSearchQuery] = useState("");

const movies = [
    {id: 1, title: "The Maze Runner", release_date: "2021", url: "https://link-to-image1.jpg"},
    {id: 2, title: "John Wick", release_date: "2024", url: "https://link-to-image2.jpg"},
    {id: 3, title: "Nowhere", release_date: "2010", url: "https://link-to-image3.jpg"},
    {id: 4, title: "Casanegra", release_date: "2009", url: "https://link-to-image4.jpg"},
];   

    const handleSearch = (e) => {
        e.preventDefault();
        alert(searchQuery); // استخدم searchQuery وليس setSearchQuery

    };

    return( <div className="home">
        <form onSubmit={handleSearch} className="search-form">
            <input
                type="text"
                placeholder="Search movies..."
                className='search-input'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
            />
            <button
                type="submit"
                className='search-button'>
                Search
            </button>
        </form>

        <div className="movies-grid">
            {movies.map((movie) => (
                <MovieCard movie={movie} key={movie.id} />
            ))}
        </div>
    </div>

    
    );
}

export default Home;
