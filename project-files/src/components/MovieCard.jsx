import '../css/MovieCard.css';

function MovieCard({ movie }) {
    function handleClick() {
    alert("clicked");
    }

    return (
    <div className="movie-card">
        <div className="movie-poster">
            <img src={movie.url} alt={movie.title} />
            <div className="movie-overlay">
                <button className="favorite-btn" onClick={handleClick}>
                🤍
                </button>
            </div>
        </div>

        <div className="movie-info">
            <h3>{movie.title}</h3>
            <p>السنة: {movie.release_date}</p>
            {movie.rating && movie.rating !== 'غير مقيم' && (
                <p className="rating">التقييم: ⭐ {movie.rating}</p>
            )}
            {movie.genres && movie.genres.length > 0 && (
                <p className="genres">النوع: {movie.genres.slice(0, 2).join(', ')}</p>
            )}
        </div>
    </div>
);
}

export default MovieCard;
