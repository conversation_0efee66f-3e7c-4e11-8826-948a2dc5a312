import { useState, useEffect } from 'react';

// Custom hook لاستخدام TVMaze API
export const useMovieAPI = () => {
  const [movies, setMovies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // دالة البحث في API
  const searchMovies = async (query) => {
    if (!query.trim()) {
      setMovies([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`https://api.tvmaze.com/search/shows?q=${encodeURIComponent(query)}`);
      
      if (!response.ok) {
        throw new Error('فشل في جلب البيانات');
      }

      const data = await response.json();
      
      // تحويل البيانات إلى الشكل المطلوب
      const formattedMovies = data.map((item, index) => ({
        id: item.show.id || index,
        title: item.show.name || 'غير معروف',
        release_date: item.show.premiered ? new Date(item.show.premiered).getFullYear() : 'غير معروف',
        url: item.show.image?.medium || item.show.image?.original || 'https://via.placeholder.com/300x400?text=No+Image',
        rating: item.show.rating?.average || 'غير مقيم',
        summary: item.show.summary || 'لا يوجد وصف متاح',
        genres: item.show.genres || [],
        status: item.show.status || 'غير معروف'
      }));

      setMovies(formattedMovies);
    } catch (err) {
      setError(err.message);
      setMovies([]);
    } finally {
      setLoading(false);
    }
  };

  // دالة لجلب المسلسلات الشائعة (افتراضياً)
  const getPopularShows = async () => {
    setLoading(true);
    setError(null);

    try {
      // جلب مسلسلات شائعة (نستخدم بحث عام)
      const popularQueries = ['game of thrones', 'breaking bad', 'stranger things', 'the office'];
      const allResults = [];

      for (const query of popularQueries) {
        const response = await fetch(`https://api.tvmaze.com/search/shows?q=${query}`);
        if (response.ok) {
          const data = await response.json();
          if (data.length > 0) {
            allResults.push(data[0]); // أخذ أول نتيجة فقط
          }
        }
      }

      const formattedMovies = allResults.map((item, index) => ({
        id: item.show.id || index,
        title: item.show.name || 'غير معروف',
        release_date: item.show.premiered ? new Date(item.show.premiered).getFullYear() : 'غير معروف',
        url: item.show.image?.medium || item.show.image?.original || 'https://via.placeholder.com/300x400?text=No+Image',
        rating: item.show.rating?.average || 'غير مقيم',
        summary: item.show.summary || 'لا يوجد وصف متاح',
        genres: item.show.genres || [],
        status: item.show.status || 'غير معروف'
      }));

      setMovies(formattedMovies);
    } catch (err) {
      setError(err.message);
      setMovies([]);
    } finally {
      setLoading(false);
    }
  };

  return {
    movies,
    loading,
    error,
    searchMovies,
    getPopularShows
  };
};
