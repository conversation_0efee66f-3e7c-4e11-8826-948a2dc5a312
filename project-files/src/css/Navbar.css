.navbar {
  background-color: #1a1a1a;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}

.navbar-brand {
  font-size: 1.8rem;
  font-weight: bold;
  color: #e50914;
}

.navbar-brand a {
  color: #e50914;
  text-decoration: none;
  transition: color 0.3s;
}

.navbar-brand a:hover {
  color: #f40612;
}

.navbar-links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.nav-link:hover {
  background-color: #e50914;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(229, 9, 20, 0.3);
}

@media (max-width: 768px) {
  .navbar {
    padding: 0.75rem 1rem;
    flex-wrap: wrap;
  }

  .navbar-brand {
    font-size: 1.4rem;
  }

  .navbar-links {
    gap: 1rem;
  }

  .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 0.5rem;
  }

  .navbar-brand {
    font-size: 1.2rem;
  }

  .navbar-links {
    gap: 0.5rem;
  }

  .nav-link {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
  }
}