.favorites {
  padding: 2rem;
  width: 100%;
  box-sizing: border-box;
}

.favorites h2 {
  margin-bottom: 2rem;
  text-align: center;
  font-size: 2.5rem;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.favorites-empty {
  text-align: center;
  padding: 4rem 2rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin: 2rem auto;
  max-width: 600px;
}

.favorites-empty h2 {
  margin-bottom: 1rem;
  font-size: 2rem;
  color: #e50914;
}

.favorites-empty p {
  color: #999;
  font-size: 1.2rem;
  line-height: 1.6;
}


/* Animation for new favorites being added */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.movies-grid > * {
  animation: fadeIn 0.3s ease-out forwards;
}

