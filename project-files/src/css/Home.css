.home {
  padding: 2rem 0;
  width: 100%;
  box-sizing: border-box;
}

.search-form {
  max-width: 600px;
  margin: 0 auto 2rem;
  display: flex;
  gap: 1rem;
  padding: 0 1rem;
  box-sizing: border-box;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 4px;
  background-color: #333;
  color: white;
  font-size: 1rem;
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #666;
}

.search-button {
  padding: 0.75rem 1.5rem;
  background-color: #e50914;
  color: white;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.search-button:hover {
  background-color: #f40612;
}


@media (max-width: 639px) {
  .home {
    padding: 1rem 0;
  }

  .search-form {
    margin-bottom: 1rem;
  }
}

.movies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
  width: 100%;
  box-sizing: border-box;
}
