.home {
  padding: 2rem 0;
  width: 100%;
  box-sizing: border-box;
}

.search-form {
  max-width: 600px;
  margin: 0 auto 2rem;
  display: flex;
  gap: 1rem;
  padding: 0 1rem;
  box-sizing: border-box;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 4px;
  background-color: #333;
  color: white;
  font-size: 1rem;
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #666;
}

.search-button {
  padding: 0.75rem 1.5rem;
  background-color: #e50914;
  color: white;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.search-button:hover {
  background-color: #f40612;
}


@media (max-width: 639px) {
  .home {
    padding: 1rem 0;
  }

  .search-form {
    margin-bottom: 1rem;
  }
}

.movies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
  width: 100%;
  box-sizing: border-box;
}

/* رسائل التحميل والخطأ */
.loading-message, .error-message, .no-results {
  text-align: center;
  padding: 2rem;
  margin: 2rem auto;
  max-width: 500px;
  border-radius: 8px;
  background-color: #2a2a2a;
  color: white;
}

.loading-message p {
  font-size: 1.2rem;
  margin: 0;
  color: #e50914;
}

.error-message {
  background-color: #4a1a1a;
  border: 1px solid #e50914;
}

.error-message p {
  color: #ff6b6b;
  margin-bottom: 1rem;
}

.no-results {
  background-color: #1a1a2a;
  border: 1px solid #646cff;
}

.no-results p {
  color: #a0a0ff;
  margin-bottom: 1rem;
}

.retry-btn {
  background-color: #e50914;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.retry-btn:hover {
  background-color: #f40612;
}
