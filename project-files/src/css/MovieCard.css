.movie-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-color: #1a1a1a;
  transition: transform 0.2s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.movie-card:hover {
  transform: translateY(-5px);
}

.movie-poster {
  position: relative;
  aspect-ratio: 2/3;
  width: 100%;
}

.movie-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.movie-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1),
    rgba(0, 0, 0, 0.8)
  );
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 1rem;
}

.movie-card:hover .movie-overlay {
  opacity: 1;
}

.favorite-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: white;
  font-size: 1.5rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.favorite-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.favorite-btn.active {
  color: #ff4757;
}

.rating-select {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 0.5rem;
}

.movie-info {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.movie-info h3 {
  font-size: 1rem;
  margin: 0;
}

.movie-info p {
  color: #999;
  font-size: 0.9rem;
  margin: 0.25rem 0;
}

.movie-info .rating {
  color: #ffd700;
  font-weight: 500;
}

.movie-info .genres {
  color: #bbb;
  font-size: 0.8rem;
}

.user-rating {
  color: #ffd700;
  font-size: 0.9rem;
  margin-top: auto;
}

@media (max-width: 768px) {
  .movie-card {
    font-size: 0.9rem;
  }

  .movie-info {
    padding: 0.75rem;
  }

  .favorite-btn {
    width: 32px;
    height: 32px;
    font-size: 1.2rem;
  }
}